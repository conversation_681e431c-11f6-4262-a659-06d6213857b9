meta {
  name: AI Scrape URL
  type: http
  seq: 8
}

post {
  url: {{baseUrl}}/api/trpc/listingScraper.aiScrapeUrl
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "url": "https://www.3djake.de/filament/pet-filament",
    "config": {
      "endpoint": "http://localhost:11434",
      "model": "llama3.2",
      "temperature": 0.1,
      "maxTokens": 4096,
      "instructions": "Focus on 3D printing filaments. Extract product name, brand, price, material type, weight, and color."
    },
    "options": {
      "headless": true,
      "timeout": 30000
    }
  }
}

docs {
  # AI Scrape URL
  
  Scrape a single URL using AI to extract product information without saving to database.
  
  ## Request Body
  
  - `url`: (string) URL to scrape
  - `config`: (optional) AI scraper configuration - if not provided, defaults will be used
    - `endpoint`: (string, optional, default: "http://**********:11434") Ollama server endpoint URL
    - `model`: (string, optional, default: "gemma3:12b") Model name to use
    - `temperature`: (number, optional, default: 0.1) Temperature for response generation (0-1)
    - `maxTokens`: (number, optional, default: 4096) Maximum tokens for the response
    - `instructions`: (string, optional) Custom instructions for product extraction
  - `options`: (optional) Scraper options - if not provided, defaults will be used
    - `headless`: (boolean, default: true) Whether to run the browser in headless mode
    - `timeout`: (number, default: 30000) Timeout in milliseconds
    - `retries`: (number, default: 3) Number of retries on failure
  
  ## Response
  
  Returns an array of extracted product data objects.
  
  ## Example Response
  
  ```json
  {
    "result": {
      "data": [
        {
          "name": "PETG Filament Red",
          "identifier": "https://example.com/product/123",
          "store": "threeDJake",
          "brand": "ExampleBrand",
          "currentPrice": 2499,
          "currency": "EUR",
          "materialType": "PETG",
          "weight": 1000,
          "color": "Red",
          "locations": ["Germany"]
        }
      ]
    }
  }
  ```
}
